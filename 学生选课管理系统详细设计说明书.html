<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生选课管理系统详细设计说明书</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        h1 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 10px;
        }
        h2 {
            color: #34495e;
            font-size: 22px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2c3e50;
            font-size: 18px;
            margin-top: 25px;
        }
        h4 {
            color: #34495e;
            font-size: 16px;
            margin-top: 20px;
        }
        .download-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #3498db;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        .download-btn:hover {
            background-color: #2980b9;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: "Consolas", "Monaco", monospace;
            font-size: 13px;
            overflow-x: auto;
        }
        .structure-tree {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: "Consolas", "Monaco", monospace;
            font-size: 13px;
            white-space: pre-line;
        }
        .flowchart {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: "Consolas", "Monaco", monospace;
            font-size: 13px;
            text-align: center;
            white-space: pre-line;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin-bottom: 5px;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .note {
            background-color: #d1ecf1;
            padding: 10px;
            border-left: 4px solid #17a2b8;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <button class="download-btn" onclick="downloadAsWord()">下载为Word文档</button>
    
    <div class="container" id="document-content">
        <div class="header">
            <h1>学生选课管理系统详细设计说明书</h1>
            <p><strong>版本：</strong>1.0 &nbsp;&nbsp;&nbsp; <strong>日期：</strong>2025年7月2日</p>
        </div>

        <h2>1 引言</h2>
        
        <h3>1.1 编写目的</h3>
        <p>本详细设计说明书的编写目的是为学生选课管理系统的开发提供详细的技术设计方案和实现指导。本文档详细描述了系统的程序结构、各模块的设计考虑、算法实现、接口定义等技术细节，为开发人员提供完整的实现依据。</p>
        
        <p><strong>预期读者包括：</strong></p>
        <ul>
            <li>系统开发人员</li>
            <li>系统测试人员</li>
            <li>系统维护人员</li>
            <li>项目管理人员</li>
            <li>技术审查人员</li>
        </ul>

        <h3>1.2 背景</h3>
        <p><strong>a. 待开发软件系统名称：</strong>学生选课管理系统 (StudentSystem)</p>
        
        <p><strong>b. 项目相关方：</strong></p>
        <ul>
            <li>任务提出者：教育管理部门</li>
            <li>开发者：软件开发团队</li>
            <li>用户：学校教务管理人员、学生、教师</li>
            <li>运行环境：Windows操作系统环境下的个人计算机或服务器</li>
        </ul>

        <h3>1.3 定义</h3>
        <ul>
            <li><strong>AVL树：</strong>一种自平衡二叉搜索树，任何节点的两个子树的高度最多相差1</li>
            <li><strong>CPair：</strong>键值对模板类，用于在AVL树中存储关联数据</li>
            <li><strong>CMyString：</strong>自定义字符串类，提供字符串操作功能</li>
            <li><strong>CRecordFile：</strong>记录文件操作类，用于二进制文件的读写操作</li>
            <li><strong>LinkedList：</strong>自定义链表类，用于处理哈希冲突和多值存储</li>
            <li><strong>BST：</strong>Binary Search Tree，二叉搜索树</li>
            <li><strong>RAII：</strong>Resource Acquisition Is Initialization，资源获取即初始化</li>
        </ul>

        <h3>1.4 参考资料</h3>
        <ul>
            <li>《数据结构与算法分析》(C++版) - Mark Allen Weiss</li>
            <li>《C++ Primer》第5版 - Stanley B. Lippman</li>
            <li>Visual Studio 2022 开发文档</li>
            <li>Windows SDK 10.0 技术文档</li>
            <li>软件工程国家标准 GB/T 8567-2006</li>
        </ul>

        <h2>2 程序系统的结构</h2>
        
        <div class="structure-tree">StudentSystem (主程序)
├── main() (主函数模块)
├── InitStudentSystem() (系统初始化模块)
├── ShowMenu() (菜单显示模块)
├── Student Management Module (学生管理模块)
│   ├── AddStudent() (添加学生)
│   ├── DeleteStudent() (删除学生)
│   ├── ModifyStudentName() (修改学生姓名)
│   ├── QueryStudentID() (按ID查询学生)
│   └── QueryStudentName() (按姓名查询学生)
├── Course Management Module (课程管理模块)
│   ├── AddCourse() (添加课程)
│   ├── DeleteCourse() (删除课程)
│   ├── ModifyCourseName() (修改课程名称)
│   ├── QueryCourseID() (按ID查询课程)
│   └── QueryCourseName() (按名称查询课程)
├── Enrollment Management Module (选课管理模块)
│   ├── AddEnrollment() (添加选课记录)
│   ├── DeleteEnrollment() (删除选课记录)
│   ├── ModifyEnrollmentScore() (修改选课成绩)
│   ├── QueryEnrollmentCurID() (按课程ID查询选课)
│   └── QueryEnrollmentStuID() (按学生ID查询选课)
├── Data Structure Module (数据结构模块)
│   ├── CAvlTree&lt;T&gt; (AVL树模板类)
│   ├── LinkedList&lt;T&gt; (链表模板类)
│   ├── CustomStack&lt;T&gt; (栈模板类)
│   ├── CustomQueue&lt;T&gt; (队列模板类)
│   └── CPair&lt;K,V&gt; (键值对模板类)
├── Utility Module (工具模块)
│   ├── CMyString (自定义字符串类)
│   ├── CRecordFile&lt;T&gt; (文件操作类)
│   └── AuxiliaryFun (辅助函数)
└── Data Storage Module (数据存储模块)
    ├── Students1.bin (学生数据文件)
    ├── CourseList1.bin (课程数据文件)
    └── Selects1.bin (选课数据文件)</div>

        <h2>3 程序1（CAvlTree）设计说明</h2>

        <h3>3.1 程序描述</h3>
        <p>CAvlTree是系统的核心数据结构模块，实现了AVL平衡二叉搜索树。该程序是一个模板类，支持泛型编程，可以存储任意类型的数据。程序特点如下：</p>
        <ul>
            <li>常驻内存的数据结构</li>
            <li>可重入的模板类</li>
            <li>支持并发读操作</li>
            <li>自动平衡维护</li>
            <li>高效的查找、插入、删除操作</li>
        </ul>

        <h3>3.2 功能</h3>
        <p>采用IPO图形式描述：</p>
        
        <div class="highlight">
            <p><strong>输入 (Input)：</strong></p>
            <ul>
                <li>泛型数据类型T的对象</li>
                <li>比较键值</li>
                <li>操作类型（插入/查找/删除）</li>
            </ul>
            
            <p><strong>处理 (Process)：</strong></p>
            <ul>
                <li>维护AVL树的平衡性</li>
                <li>执行旋转操作</li>
                <li>更新节点高度</li>
                <li>管理树结构</li>
            </ul>
            
            <p><strong>输出 (Output)：</strong></p>
            <ul>
                <li>查询结果节点指针</li>
                <li>操作成功/失败状态</li>
                <li>树的统计信息</li>
            </ul>
        </div>

        <p><strong>主要功能包括：</strong></p>
        <ul>
            <li>Insert(value) - 插入节点</li>
            <li>Search(value) - 查找节点</li>
            <li>Delete(value) - 删除节点</li>
            <li>GetRoot() - 获取根节点</li>
            <li>Size() - 获取节点数量</li>
            <li>PreOrder() - 前序遍历</li>
            <li>InOrder() - 中序遍历</li>
            <li>PostOrder() - 后序遍历</li>
        </ul>

        <h3>3.3 性能</h3>
        <ul>
            <li><strong>时间复杂度：</strong>查找、插入、删除操作均为O(log n)</li>
            <li><strong>空间复杂度：</strong>O(n)，其中n为节点数量</li>
            <li><strong>平衡性：</strong>任意节点的左右子树高度差不超过1</li>
            <li><strong>并发性：</strong>支持多线程读操作，写操作需要同步</li>
            <li><strong>精度要求：</strong>高度计算精确，平衡因子计算准确</li>
            <li><strong>灵活性：</strong>支持任意可比较的数据类型</li>
            <li><strong>时间特性：</strong>所有操作保证对数时间复杂度</li>
        </ul>

        <h3>3.4 输入项</h3>
        <table>
            <tr>
                <th>输入项名称</th>
                <th>标识符</th>
                <th>数据类型</th>
                <th>数据格式</th>
                <th>有效范围</th>
                <th>输入方式</th>
                <th>数量和频度</th>
                <th>输入媒体</th>
                <th>数据来源</th>
                <th>安全保密条件</th>
            </tr>
            <tr>
                <td>节点值</td>
                <td>value</td>
                <td>模板类型T</td>
                <td>依赖于T的定义</td>
                <td>依赖于T的约束</td>
                <td>函数参数传递</td>
                <td>每次操作1个</td>
                <td>内存</td>
                <td>调用模块</td>
                <td>无特殊要求</td>
            </tr>
            <tr>
                <td>比较键</td>
                <td>key</td>
                <td>比较键类型</td>
                <td>支持&lt;、&gt;、==操作</td>
                <td>任意可比较类型</td>
                <td>对象成员访问</td>
                <td>每次比较1个</td>
                <td>内存</td>
                <td>数据对象</td>
                <td>无特殊要求</td>
            </tr>
            <tr>
                <td>操作标识</td>
                <td>operation</td>
                <td>枚举类型</td>
                <td>INSERT/SEARCH/DELETE</td>
                <td>预定义操作类型</td>
                <td>函数调用</td>
                <td>每次操作1个</td>
                <td>内存</td>
                <td>调用模块</td>
                <td>无特殊要求</td>
            </tr>
        </table>

        <h3>3.5 输出项</h3>
        <table>
            <tr>
                <th>输出项名称</th>
                <th>标识符</th>
                <th>数据类型</th>
                <th>数据格式</th>
                <th>有效范围</th>
                <th>输出形式</th>
                <th>数量和频度</th>
                <th>输出媒体</th>
                <th>图形符号说明</th>
                <th>安全保密条件</th>
            </tr>
            <tr>
                <td>节点指针</td>
                <td>TreeNode*</td>
                <td>指针类型</td>
                <td>内存地址</td>
                <td>有效内存地址或nullptr</td>
                <td>返回值</td>
                <td>每次查询1个</td>
                <td>内存</td>
                <td>指向树节点的指针</td>
                <td>无特殊要求</td>
            </tr>
            <tr>
                <td>操作状态</td>
                <td>bool</td>
                <td>布尔类型</td>
                <td>true/false</td>
                <td>{true, false}</td>
                <td>返回值</td>
                <td>每次操作1个</td>
                <td>内存</td>
                <td>操作成功标志</td>
                <td>无特殊要求</td>
            </tr>
            <tr>
                <td>节点数量</td>
                <td>int</td>
                <td>整数类型</td>
                <td>32位整数</td>
                <td>0到INT_MAX</td>
                <td>返回值</td>
                <td>按需查询</td>
                <td>内存</td>
                <td>树中节点总数</td>
                <td>无特殊要求</td>
            </tr>
        </table>

        <h3>3.6 算法</h3>
        
        <h4>AVL树插入算法详细步骤：</h4>
        
        <p><strong>1. 标准BST插入：</strong></p>
        <div class="code-block">if (node == null)
    return new TreeNode(value)
if (value &lt; node.value)
    node.left = Insert(node.left, value)
else if (value &gt; node.value)
    node.right = Insert(node.right, value)
else
    return node  // 重复值不插入</div>

        <p><strong>2. 更新高度计算公式：</strong></p>
        <div class="code-block">height(node) = 1 + max(height(node.left), height(node.right))</div>

        <p><strong>3. 平衡因子计算公式：</strong></p>
        <div class="code-block">balance_factor = height(node.left) - height(node.right)</div>

        <p><strong>4. 旋转操作计算步骤：</strong></p>
        <ul>
            <li><strong>LL情况：</strong>balance > 1 且 value < node.left.value → 执行右旋转</li>
            <li><strong>RR情况：</strong>balance < -1 且 value > node.right.value → 执行左旋转</li>
            <li><strong>LR情况：</strong>balance > 1 且 value > node.left.value → 先左旋转后右旋转</li>
            <li><strong>RL情况：</strong>balance < -1 且 value < node.right.value → 先右旋转后左旋转</li>
        </ul>

        <p><strong>右旋转算法：</strong></p>
        <div class="code-block">TreeNode* RightRotate(TreeNode* y) {
    TreeNode* x = y-&gt;m_pLeft;
    TreeNode* T2 = x-&gt;m_pRight;
    
    // 执行旋转
    x-&gt;m_pRight = y;
    y-&gt;m_pLeft = T2;
    
    // 更新高度
    UpdateHeight(y);
    UpdateHeight(x);
    
    return x;  // 新的根节点
}</div>

        <p><strong>左旋转算法：</strong></p>
        <div class="code-block">TreeNode* LeftRotate(TreeNode* x) {
    TreeNode* y = x-&gt;m_pRight;
    TreeNode* T2 = y-&gt;m_pLeft;
    
    // 执行旋转
    y-&gt;m_pLeft = x;
    x-&gt;m_pRight = T2;
    
    // 更新高度
    UpdateHeight(x);
    UpdateHeight(y);
    
    return y;  // 新的根节点
}</div>
    </div>

    <script>
        function downloadAsWord() {
            // 获取文档内容
            const content = document.getElementById('document-content').innerHTML;
            
            // 创建完整的HTML文档结构
            const htmlContent = `
<!DOCTYPE html>
<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'>
<head>
    <meta charset='utf-8'>
    <title>学生选课管理系统详细设计说明书</title>
    <!--[if gte mso 9]>
    <xml>
        <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>90</w:Zoom>
            <w:DoNotPromptForConvert/>
            <w:DoNotShowInsertionsAndDeletions/>
        </w:WordDocument>
    </xml>
    <![endif]-->
    <style>
        body {
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
        }
        h1 { color: #2c3e50; font-size: 24px; text-align: center; }
        h2 { color: #34495e; font-size: 20px; border-left: 4px solid #3498db; padding-left: 15px; }
        h3 { color: #2c3e50; font-size: 16px; }
        h4 { color: #34495e; font-size: 14px; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #000; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .code-block, .structure-tree, .flowchart {
            background-color: #f8f9fa;
            border: 1px solid #ccc;
            padding: 10px;
            margin: 10px 0;
            font-family: "Consolas", "Monaco", monospace;
            font-size: 12px;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .note {
            background-color: #d1ecf1;
            padding: 10px;
            border-left: 4px solid #17a2b8;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    ${content}
</body>
</html>`;
            
            // 创建Blob对象
            const blob = new Blob([htmlContent], {
                type: 'application/msword'
            });
            
            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '学生选课管理系统详细设计说明书.doc';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            alert('文档下载已开始！');
        }
    </script>
</body>
</html>
