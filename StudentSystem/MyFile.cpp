#include "MyFile.h"
//====================== Student 特化 ======================

template<>
int CRecordFile<Student>::CalculateSize(const Student& s) {
    //删除标志(1) + 学生ID(4) + 姓名长度字段(4) + 姓名内容
    return sizeof(bool) + sizeof(int) + sizeof(int) + s.m_StudentName.Length();
}

template<>
void CRecordFile<Student>::WriteAt(const Student& s) {
    //写入 Student 记录的全过程：
    //[总长度][删除标志][学生ID][姓名长度][姓名内容]

    //1. 计算各字段长度
    int nameLen = s.m_StudentName.Length();
    int totalLen = CalculateSize(s);

    //2. 写入总长度字段（int）
    fwrite(&totalLen, sizeof(int), 1, m_fp);

    //3. 写入删除标志（bool）
    fwrite(&s.m_IsDeleted, sizeof(bool), 1, m_fp);

    //4. 写入学生ID（int）
    fwrite(&s.m_StudentID, sizeof(int), 1, m_fp);

    //5. 写入姓名长度（int）
    fwrite(&nameLen, sizeof(int), 1, m_fp);

    //6. 写入姓名内容（char[nameLen]）
    fwrite(s.m_StudentName.toCString(), 1, nameLen, m_fp);
}

template<>
bool CRecordFile<Student>::ReadStruct(Student& s) {
    //读取 Student 记录的全过程：
    //1. 读取并忽略总长度字段
    int totalLen;
    fread(&totalLen, sizeof(int), 1, m_fp);

    //2. 读取删除标志（bool），若已删除则跳过
    fread(&s.m_IsDeleted, sizeof(bool), 1, m_fp);
    if (s.m_IsDeleted) return false;

    //3. 读取学生ID（int）
    fread(&s.m_StudentID, sizeof(int), 1, m_fp);

    //4. 读取姓名长度（int）
    int nameLen;
    fread(&nameLen, sizeof(int), 1, m_fp);

    //5. 根据 nameLen 分配缓冲，读取姓名内容
    char* buf = new char[nameLen + 1];
    fread(buf, 1, nameLen, m_fp);
    buf[nameLen] = '\0';
    s.m_StudentName = CMyString(buf);
    delete[] buf;

    //6. 完成读取，返回true
    return true;
}

//====================== Course 特化 ======================

template<>
int CRecordFile<Course>::CalculateSize(const Course& c) {
    //删除标志(1) + 课程ID(4) + 名称长度字段(4) + 名称内容
    return sizeof(bool) + sizeof(int) + sizeof(int) + c.m_CourseName.Length();
}

template<>
void CRecordFile<Course>::WriteAt(const Course& c) {
    //写入 Course 记录：
    //[总长度][删除标志][课程ID][名称长度][名称内容]

    int nameLen = c.m_CourseName.Length();
    int totalLen = CalculateSize(c);

    //1. 写入总长度
    fwrite(&totalLen, sizeof(int), 1, m_fp);
    //2. 写入删除标志
    fwrite(&c.m_IsDeleted, sizeof(bool), 1, m_fp);
    //3. 写入课程ID
    fwrite(&c.m_CourseID, sizeof(int), 1, m_fp);
    //4. 写入名称长度
    fwrite(&nameLen, sizeof(int), 1, m_fp);
    //5. 写入名称内容
    fwrite(c.m_CourseName.toCString(), 1, nameLen, m_fp);
}

template<>
bool CRecordFile<Course>::ReadStruct(Course& c) {
    //读取 Course 记录：
    //1. 读取并忽略总长度字段
    int totalLen;
    fread(&totalLen, sizeof(int), 1, m_fp);

    //2. 读取删除标志，已删除则返回false
    fread(&c.m_IsDeleted, sizeof(bool), 1, m_fp);
    if (c.m_IsDeleted) return false;

    //3. 读取课程ID
    fread(&c.m_CourseID, sizeof(int), 1, m_fp);

    //4. 读取名称长度
    int nameLen;
    fread(&nameLen, sizeof(int), 1, m_fp);

    //5. 读取名称内容
    char* buf = new char[nameLen + 1];
    fread(buf, 1, nameLen, m_fp);
    buf[nameLen] = '\0';
    c.m_CourseName = CMyString(buf);
    delete[] buf;

    //6. 成功返回
    return true;
}

//=================== Enrollment 特化 =====================
template<>
int CRecordFile<Enrollment>::CalculateSize(const Enrollment& e) {
    //删除标志(1) + 学生ID(4) + 课程ID(4) + 分数(4)
    return sizeof(bool) + sizeof(int) * 3;
}

template<>
void CRecordFile<Enrollment>::WriteAt(const Enrollment& e) {
    //写入 Enrollment 记录：
    //[总长度][删除标志][学生ID][课程ID][分数]
    count++;
    int totalLen = CalculateSize(e);
    //1. 写入总长度字段
    fwrite(&totalLen, sizeof(int), 1, m_fp);
    //2. 写入删除标志
    fwrite(&e.m_IsDeleted, sizeof(bool), 1, m_fp);
    //3. 写入学生ID
    fwrite(&e.m_StudentID, sizeof(int), 1, m_fp);
    //4. 写入课程ID
    fwrite(&e.m_CourseID, sizeof(int), 1, m_fp);
    //5. 写入分数
    fwrite(&e.m_Score, sizeof(int), 1, m_fp);
}

template<>
bool CRecordFile<Enrollment>::ReadStruct(Enrollment& e) {
    int totalLen;
    fread(&totalLen, sizeof(int), 1, m_fp);
    //2. 读取删除标志并判断
    fread(&e.m_IsDeleted, sizeof(bool), 1, m_fp);
    if (e.m_IsDeleted) {
        return false;
    }
    //3. 读取学生ID
    fread(&e.m_StudentID, sizeof(int), 1, m_fp);
    //4. 读取课程ID
    fread(&e.m_CourseID, sizeof(int), 1, m_fp);
    //5. 读取分数字段
    fread(&e.m_Score, sizeof(int), 1, m_fp);
    //6. 成功返回
    return true;
}