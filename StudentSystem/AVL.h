#pragma once

#include <iostream>
#include <functional> 
#include "CMyString.h"
#include "MyStack.h"
#include "MyQueue.h"
#include <stdexcept>  
//#include "MyFile.h"


template <typename T>
T MyMax(T a, T b) {
    return (a > b) ? a : b;
}


// 键值对类
template<typename KEY, typename VALUE>
class CPair {
public:
    CPair() {}
    CPair(const KEY& Key, const VALUE& Value) : m_Key(Key), m_Value(Value) {}

    //friend std::ostream& operator<<(std::ostream& os, const CPair& p) {
    //    os << "Key(" << p.m_Key << ")-Value(ID:" << p.m_Value.m_CourseID << ", Name:" << p.m_Value.m_CourseName << ")";
    //    return os;
    //}

    // 基于键的比较操作符
    bool operator>(const CPair& p) const { return m_Key > p.m_Key; }
    bool operator<(const CPair& p) const { return m_Key < p.m_Key; }
    bool operator==(const CPair& p) const { return m_Key == p.m_Key; }

    KEY   m_Key;
    VALUE m_Value;
};


template<typename TYPE>
class CAvlTree {
public:
    // 树节点结构体
    struct TreeNode {
        TYPE      m_Value;
        TreeNode* m_pLeft;
        TreeNode* m_pRight;
        TreeNode* m_pParent;
        int       m_nHeight;

        TreeNode(const TYPE& val, TreeNode* parent = nullptr)
            : m_Value(val), m_pLeft(nullptr), m_pRight(nullptr), m_pParent(parent), m_nHeight(0) {}
    };

    // 为方便使用，定义类型别名
    using Node = TreeNode;
    using Visit = std::function<void(const TYPE&)>; // 定义访问者函数类型

    // 构造函数
    CAvlTree() : m_pRoot(nullptr), m_nSize(0) {}

    ~CAvlTree(){
        Clear();
    }

    // 插入节点操作 O(log n)
    void Insert(const TYPE& Val) {
        if (!m_pRoot) {
            m_pRoot = new Node(Val);
            m_nSize++;
            return;
        }

        Node* cur = m_pRoot;
        Node* parent = nullptr;

        // 1. 迭代寻找插入点
        while (cur) {
            parent = cur;
            if (Val < cur->m_Value) {
                cur = cur->m_pLeft;
            }
            else if (Val > cur->m_Value) {
                cur = cur->m_pRight;
            }
            else {
                //cur = cur->m_pLeft;
                //return;
            }
        }

        // 2. 创建并链接新节点
        Node* newNode = new Node(Val, parent);
        if (Val < parent->m_Value) {
            parent->m_pLeft = newNode;
        }
        else {
            parent->m_pRight = newNode;
        }
        m_nSize++;

        // 3. 自下而上更新高度并旋转保持平衡
        RebalanceUp(parent);
    }

    // 删除节点操作 O(log n)
    void Remove(const TYPE& Val) {
        Node* nodeToDelete = SearchNode(Val);
        if (!nodeToDelete) return; // 未找到，直接返回

        Node* nodeToActualDelete = nodeToDelete;

        // 情况1：要删除的节点有两个子节点
        if (nodeToDelete->m_pLeft && nodeToDelete->m_pRight) {
            // 找到其前驱（左子树的最大节点）来替代它
            Node* predecessor = nodeToDelete->m_pLeft;
            while (predecessor->m_pRight) {
                predecessor = predecessor->m_pRight;
            }
            // 将前驱的值复制过来，问题转化为删除前驱节点
            nodeToDelete->m_Value = predecessor->m_Value;
            nodeToActualDelete = predecessor;
        }

        // 情况2和3：要删除的节点有0或1个子节点
        Node* child = nodeToActualDelete->m_pLeft ? nodeToActualDelete->m_pLeft : nodeToActualDelete->m_pRight;
        Node* parentOfActualDelete = nodeToActualDelete->m_pParent;

        // 重新链接父节点和子节点
        if (!parentOfActualDelete) {
            m_pRoot = child;
        }
        else if (nodeToActualDelete == parentOfActualDelete->m_pLeft) {
            parentOfActualDelete->m_pLeft = child;
        }
        else {
            parentOfActualDelete->m_pRight = child;
        }

        // 更新子节点的父指针
        if (child) {
            child->m_pParent = parentOfActualDelete;
        }

        // 记录需要开始重新平衡的节点
        Node* rebalanceStartNode = parentOfActualDelete;

        delete nodeToActualDelete;
        m_nSize--;

        // 从实际删除节点的父节点开始，向上回溯并平衡树
        RebalanceUp(rebalanceStartNode);
    }

    // 查找节点操作 O(log n)
    Node* Search(const TYPE& Val) const {
        Node* cur = m_pRoot;
        while (cur) {
            if (Val < cur->m_Value) {
                cur = cur->m_pLeft;
            }
            else if (Val > cur->m_Value) {
                cur = cur->m_pRight;
            }
            else {
                return cur; // 找到匹配节点
            }
        }
        return nullptr; // 未找到
    }

    // 查找最小值 O(log n)
    const TYPE* FindMin() const {
        if (!m_pRoot) return nullptr;
        Node* cur = m_pRoot;
        while (cur->m_pLeft) {
            cur = cur->m_pLeft;
        }
        return &cur->m_Value;
    }

    // 查找最大值 O(log n)
    const TYPE* FindMax() const {
        if (!m_pRoot) return nullptr;
        Node* cur = m_pRoot;
        while (cur->m_pRight) {
            cur = cur->m_pRight;
        }
        return &cur->m_Value;
    }

    // 前序遍历 (根-左-右)
    void PreOrder(Visit visitor) const {
        if (!m_pRoot) return;
        CustomStack<Node*> stack;
        stack.Push(m_pRoot);
        while (!stack.IsEmpty()) {
            Node* cur = stack.Peek();
            //std::cout << m_count++ << std::endl;
            stack.Pop();
            visitor(cur->m_Value); // 访问节点
            if (cur->m_pRight) stack.Push(cur->m_pRight);
            if (cur->m_pLeft) stack.Push(cur->m_pLeft);
        }
    }

    // 中序遍历 (左-根-右)
    void InOrder(Visit visitor) const {
        if (!m_pRoot) return;
        CustomStack<Node*> stack;
        Node* cur = m_pRoot;
        while (cur || !stack.IsEmpty()) {
            while (cur) {
                stack.Push(cur);
                cur = cur->m_pLeft;
            }
            cur = stack.Peek();
            stack.Pop();
            visitor(cur->m_Value); // 访问节点
            cur = cur->m_pRight;
        }
    }

    // 后序遍历 (左-右-根)
    void PostOrder(Visit visitor) const {
        if (!m_pRoot) return;
        CustomStack<Node*> s1, s2;
        s1.Push(m_pRoot);
        while (!s1.IsEmpty()) {
            Node* cur = s1.Peek();
            s1.Pop();
            s2.Push(cur);
            if (cur->m_pLeft) s1.Push(cur->m_pLeft);
            if (cur->m_pRight) s1.Push(cur->m_pRight);
        }
        while (!s2.IsEmpty()) {
            visitor(s2.Peek()->m_Value); // 访问节点
            s2.Pop();
        }
    }

    // 层序遍历打印树结构
    void PrintTree() const {
        if (!m_pRoot) {
            std::cout << "树是空的。" << std::endl;
            return;
        }

        CustomQueue<Node*> q;
        q.Enqueue(m_pRoot);
        int level = 0;

        while (!q.IsEmpty()) {
            int nodesAtCurrentLevel = q.Size();
            std::cout << "Level " << level++ << ": ";
            for (int i = 0; i < nodesAtCurrentLevel; ++i) {
                Node* t = q.Dequeue();
                if (t) {
                    std::cout << t->m_Value << "[H:" << t->m_nHeight << "] ";
                    if (t->m_pLeft) q.Enqueue(t->m_pLeft);
                    if (t->m_pRight) q.Enqueue(t->m_pRight);
                }
            }
            std::cout << std::endl;
        }
    }

    // 获取树中节点数量
    int Size() const { return m_nSize; }
    // 判断树是否为空
    bool Empty() const { return m_nSize == 0; }
    // 获取树的根节点
    Node* GetRoot() const { return m_pRoot; }


private:
    Node* m_pRoot; // 树的根节点
    int   m_nSize; // 树中节点数量
    int m_count =0 ; // 计数器

    // =================================================================
    // 内部辅助函数
    // =================================================================

    // 清空树，释放所有节点内存 (使用迭代后序遍历)
    void Clear() {
        if (!m_pRoot) return;

        // 使用两个栈模拟后序遍历来进行安全的删除
        CustomStack<Node*> s1, s2;
        s1.Push(m_pRoot);

        while (!s1.IsEmpty()) {
            Node* cur = s1.Peek();
            s1.Pop();
            s2.Push(cur); // 将节点按后序遍历的逆序压入 s2

            if (cur->m_pLeft) s1.Push(cur->m_pLeft);
            if (cur->m_pRight) s1.Push(cur->m_pRight);
        }

        // 从 s2 中依次弹出并删除节点
        while (!s2.IsEmpty()) {
            Node* toDelete = s2.Peek();
            s2.Pop();
            delete toDelete;
        }

        m_pRoot = nullptr;
        m_nSize = 0;
    }

    // 从指定节点向根方向依次更新高度并检查平衡
    void RebalanceUp(Node* node) {
        while (node) {
            UpdateHeight(node);
            int bf = BalanceFactor(node);

            // 如果节点失衡，执行旋转操作
            if (bf > 1) { // 左子树过高 (LL 或 LR 情况)
                if (BalanceFactor(node->m_pLeft) < 0) { // LR
                    RotateLeft(node->m_pLeft);
                }
                RotateRight(node); // LL
            }
            else if (bf < -1) { // 右子树过高 (RR 或 RL 情况)
                if (BalanceFactor(node->m_pRight) > 0) { // RL
                    RotateRight(node->m_pRight);
                }
                RotateLeft(node); // RR
            }

            // 旋转后，新的子树根的高度可能已更新，但其父节点的高度还需要更新
            // 因此我们继续向上回溯。
            node = node->m_pParent;
        }
    }

    // 内部查找函数
    Node* SearchNode(const TYPE& Val) const {
        return Search(Val);
    }

    // 获取节点高度 (空节点高度为 -1)
    int Height(Node* t) const { return t ? t->m_nHeight : -1; }

    // 更新节点高度
    void UpdateHeight(Node* t) {
        if (t) {
            t->m_nHeight = 1 + MyMax(Height(t->m_pLeft), Height(t->m_pRight));
        }
    }

    // 计算节点的平衡因子
    int BalanceFactor(Node* t) const { return t ? Height(t->m_pLeft) - Height(t->m_pRight) : 0; }

    // 左旋
    void RotateLeft(Node* x) {
        Node* y = x->m_pRight;
        Node* p = x->m_pParent;

        x->m_pRight = y->m_pLeft;
        if (y->m_pLeft) {
            y->m_pLeft->m_pParent = x;
        }

        y->m_pLeft = x;
        x->m_pParent = y;

        y->m_pParent = p;
        if (!p) {
            m_pRoot = y;
        }
        else if (p->m_pLeft == x) {
            p->m_pLeft = y;
        }
        else {
            p->m_pRight = y;
        }

        UpdateHeight(x);
        UpdateHeight(y);
    }

    // 右旋
    void RotateRight(Node* x) {
        Node* y = x->m_pLeft;
        Node* p = x->m_pParent;

        x->m_pLeft = y->m_pRight;
        if (y->m_pRight) {
            y->m_pRight->m_pParent = x;
        }

        y->m_pRight = x;
        x->m_pParent = y;

        y->m_pParent = p;
        if (!p) {
            m_pRoot = y;
        }
        else if (p->m_pLeft == x) {
            p->m_pLeft = y;
        }
        else {
            p->m_pRight = y;
        }

        UpdateHeight(x);
        UpdateHeight(y);
    }
};

//// =================================================================
//// 示例 main 函数
//// =================================================================
//int main() {
//    // 使用 Course 作为值的 AVL 树
//    CAvlTree<CPair<int, Course>> courseTree;
//
//    // 创建课程数据
//    Course c1 = { 101, "计算机科学导论" };
//    Course c2 = { 205, "数据结构" };
//    Course c3 = { 310, "算法分析" };
//    Course c4 = { 150, "离散数学" };
//    Course c5 = { 250, "操作系统" };
//    Course c6 = { 401, "计算机网络" };
//    Course c7 = { 50, "编程基础" };
//
//    // 插入数据
//    std::cout << "--- 插入节点 ---\n";
//    courseTree.Insert({ c1.m_CourseID, c1 });
//    courseTree.Insert({ c2.m_CourseID, c2 });
//    courseTree.Insert({ c3.m_CourseID, c3 });
//    courseTree.Insert({ c4.m_CourseID, c4 });
//    courseTree.Insert({ c5.m_CourseID, c5 });
//    courseTree.Insert({ c6.m_CourseID, c6 });
//    courseTree.Insert({ c7.m_CourseID, c7 });
//
//    std::cout << "当前树的大小: " << courseTree.Size() << std::endl;
//    courseTree.PrintTree();
//
//    // 查找最大和最小值
//    std::cout << "\n--- 查找最大/最小值 ---\n";
//    const auto* minCoursePair = courseTree.FindMin();
//    const auto* maxCoursePair = courseTree.FindMax();
//    if (minCoursePair) std::cout << "最小课程: " << *minCoursePair << std::endl;
//    if (maxCoursePair) std::cout << "最大课程: " << *maxCoursePair << std::endl;
//
//
//    std::cout << "\n--- 各种遍历 ---" << std::endl;
//    // 使用 lambda 表达式作为访问者进行前序遍历
//    std::cout << "前序遍历: ";
//    courseTree.PreOrder([](const CPair<int, Course>& p) {
//        std::cout << p.m_Value.m_CourseID << " ";
//        });
//    std::cout << std::endl;
//
//    // 中序遍历
//    std::cout << "中序遍历: ";
//    courseTree.InOrder([](const CPair<int, Course>& p) {
//        std::cout << p.m_Value.m_CourseID << " ";
//        });
//    std::cout << std::endl;
//
//    // 后序遍历
//    std::cout << "后序遍历: ";
//    courseTree.PostOrder([](const CPair<int, Course>& p) {
//        std::cout << p.m_Value.m_CourseID << " ";
//        });
//    std::cout << std::endl;
//
//    // 删除节点
//    std::cout << "\n--- 删除节点 (ID: 205) ---\n";
//    courseTree.Remove({ 205, {} }); // 仅用 key 来查找并删除
//    std::cout << "删除后树的大小: " << courseTree.Size() << std::endl;
//    courseTree.PrintTree();
//
//    std::cout << "\n--- 删除根节点 (ID: 150) ---\n";
//    courseTree.Remove({ 150, {} });
//    std::cout << "删除后树的大小: " << courseTree.Size() << std::endl;
//    courseTree.PrintTree();
//
//    std::cout << "\n--- 测试清空树 ---\n";
//    // 析构函数会自动调用 Clear()，这里我们手动调用来测试
//    // ~CAvlTree() is called at the end of main
//
//    return 0;
//}
