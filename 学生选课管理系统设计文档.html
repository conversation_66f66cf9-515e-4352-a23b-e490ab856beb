<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>?????ι????????????????</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        h1 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 10px;
        }
        h2 {
            color: #34495e;
            font-size: 22px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2c3e50;
            font-size: 18px;
            margin-top: 25px;
        }
        h4 {
            color: #34495e;
            font-size: 16px;
            margin-top: 20px;
        }
        .download-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #3498db;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        .download-btn:hover {
            background-color: #2980b9;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: "Consolas", "Monaco", monospace;
            font-size: 13px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .structure-tree {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: "Consolas", "Monaco", monospace;
            font-size: 13px;
            white-space: pre-line;
        }
        .flowchart {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: "Consolas", "Monaco", monospace;
            font-size: 13px;
            text-align: center;
            white-space: pre-line;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin-bottom: 5px;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .note {
            background-color: #d1ecf1;
            padding: 10px;
            border-left: 4px solid #17a2b8;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <button class="download-btn" onclick="downloadAsWord()">?????Word???</button>
    
    <div class="container" id="document-content">
        <div class="header">
            <h1>?????ι????????????????</h1>
            <p><strong>?汾??</strong>1.0 &nbsp;&nbsp;&nbsp; <strong>?????</strong>2025??7??2??</p>
        </div>

        <h2>1 ????</h2>
        
        <h3>1.1 ??д???</h3>
        <p>???????????????д???????????ι?????????????????????????????????????????????????????????????????????????????????????????????????????????????????????</p>
        
        <p><strong>???????????</strong></p>
        <ul>
            <li>?????????</li>
            <li>?????????</li>
            <li>????????</li>
            <li>??????????</li>
            <li>??????????</li>
        </ul>

        <h3>1.2 ????</h3>
        <p><strong>a. ????????????????</strong>?????ι????? (StudentSystem)</p>
        
        <p><strong>b. ?????????</strong></p>
        <ul>
            <li>????????????????????</li>
            <li>?????????????????</li>
            <li>??????У????????????????????</li>
            <li>???л?????Windows???????????????????????????</li>
        </ul>

        <h3>1.3 ????</h3>
        <ul>
            <li><strong>AVL????</strong>????????????????????κν?????????????????????1</li>
            <li><strong>CPair??</strong>????????????????AVL???д?????????</li>
            <li><strong>CMyString??</strong>???????????????????????????</li>
            <li><strong>CRecordFile??</strong>???????????????????????????д????</li>
            <li><strong>LinkedList??</strong>?????????????????????????????</li>
            <li><strong>BST??</strong>Binary Search Tree????????????</li>
            <li><strong>RAII??</strong>Resource Acquisition Is Initialization???????????????</li>
        </ul>

        <h3>1.4 ?ο?????</h3>
        <ul>
            <li>?????????????????(C++??) - Mark Allen Weiss</li>
            <li>??C++ Primer????5?? - Stanley B. Lippman</li>
            <li>Visual Studio 2022 ???????</li>
            <li>Windows SDK 10.0 ???????</li>
            <li>???????????? GB/T 8567-2006</li>
        </ul>

        <h2>2 ?????????</h2>
        
        <div class="structure-tree">StudentSystem (??????)
?????? main() (?????????)
?????? InitStudentSystem() (??????????)
?????? ShowMenu() (?????????)
?????? Student Management Module (??????????)
??   ?????? AddStudent() (??????)
??   ?????? DeleteStudent() (??????)
??   ?????? ModifyStudentName() (??????????)
??   ?????? QueryStudentID() (??ID??????)
??   ?????? QueryStudentName() (????????????)
?????? Course Management Module (?γ???????)
??   ?????? AddCourse() (???γ?)
??   ?????? DeleteCourse() (????γ?)
??   ?????? ModifyCourseName() (???γ?????)
??   ?????? QueryCourseID() (??ID????γ?)
??   ?????? QueryCourseName() (?????????γ?)
?????? Enrollment Management Module (??ι??????)
??   ?????? AddEnrollment() (?????μ??)
??   ?????? DeleteEnrollment() (?????μ??)
??   ?????? ModifyEnrollmentScore() (?????γ??)
??   ?????? QueryEnrollmentCurID() (???γ?ID??????)
??   ?????? QueryEnrollmentStuID() (?????ID??????)
?????? Data Structure Module (????????)
??   ?????? CAvlTree&lt;T&gt; (AVL???????)
??   ?????? LinkedList&lt;T&gt; (?????????)
??   ?????? CustomStack&lt;T&gt; (??????)
??   ?????? CustomQueue&lt;T&gt; (?????????)
??   ?????? CPair&lt;K,V&gt; (??????????)
?????? Utility Module (???????)
??   ?????? CMyString (????????????)
??   ?????? CRecordFile&lt;T&gt; (?????????)
??   ?????? AuxiliaryFun (????????)
?????? Data Storage Module (????????)
    ?????? Students1.bin (??????????)
    ?????? CourseList1.bin (?γ????????)
    ?????? Selects1.bin (??????????)</div>

        <h2>3 ????1??CAvlTree????????</h2>

        <h3>3.1 ????????</h3>
        <p>CAvlTree??????????????????????AVL???????????????ó?????????????????????????????????????????????????????</p>
        <ul>
            <li>????????????</li>
            <li>????????????</li>
            <li>????????????</li>
            <li>?????????</li>
            <li>??Ч?????????????????</li>
        </ul>

        <h3>3.2 ????</h3>
        <p>????IPO??????????</p>
        
        <div class="highlight">
            <p><strong>???? (Input)??</strong></p>
            <ul>
                <li>????????????T?????</li>
                <li>?????</li>
                <li>?????????????/????/?????</li>
            </ul>
            
            <p><strong>???? (Process)??</strong></p>
            <ul>
                <li>???AVL?????????</li>
                <li>??????????</li>
                <li>????????</li>
                <li>????????</li>
            </ul>
            
            <p><strong>??? (Output)??</strong></p>
            <ul>
                <li>????????????</li>
                <li>???????/?????</li>
                <li>??????????</li>
            </ul>
        </div>

        <p><strong>????????????</strong></p>
        <ul>
            <li>Insert(value) - ??????</li>
            <li>Search(value) - ??????</li>
            <li>Delete(value) - ??????</li>
            <li>GetRoot() - ????????</li>
            <li>Size() - ??????????</li>
            <li>PreOrder() - ??????</li>
            <li>InOrder() - ???????</li>
            <li>PostOrder() - ???????</li>
        </ul>

        <h3>3.3 ????</h3>
        <ul>
            <li><strong>??临????</strong>???????????????????O(log n)</li>
            <li><strong>??临????</strong>O(n)??????n????????</li>
            <li><strong>??????</strong>???????????????????????1</li>
            <li><strong>???????</strong>??????????????д??????????</li>
            <li><strong>???????</strong>?????????????????????</li>
            <li><strong>??????</strong>???????????????????</li>
            <li><strong>????????</strong>???в????????????临???</li>
        </ul>

        <h3>3.4 ??????</h3>
        <table>
            <tr>
                <th>??????????</th>
                <th>?????</th>
                <th>????????</th>
                <th>??????</th>
                <th>??Ч??Χ</th>
                <th>?????</th>
                <th>?????????</th>
                <th>???????</th>
                <th>???????</th>
                <th>???????????</th>
            </tr>
            <tr>
                <td>????</td>
                <td>value</td>
                <td>???????T</td>
                <td>??????T?????</td>
                <td>??????T?????</td>
                <td>????????????</td>
                <td>??β???1??</td>
                <td>???</td>
                <td>???????</td>
                <td>?????????</td>
            </tr>
            <tr>
                <td>????</td>
                <td>key</td>
                <td>????????</td>
                <td>???&lt;??&gt;??==????</td>
                <td>???????????</td>
                <td>??????????</td>
                <td>??α??1??</td>
                <td>???</td>
                <td>???????</td>
                <td>?????????</td>
            </tr>
            <tr>
                <td>???????</td>
                <td>operation</td>
                <td>???????</td>
                <td>INSERT/SEARCH/DELETE</td>
                <td>????????????</td>
                <td>????????</td>
                <td>??β???1??</td>
                <td>???</td>
                <td>???????</td>
                <td>?????????</td>
            </tr>
        </table>

        <h3>3.5 输出项</h3>
        <table>
            <tr>
                <th>输出项名称</th>
                <th>标识符</th>
                <th>数据类型</th>
                <th>数据格式</th>
                <th>有效范围</th>
                <th>输出形式</th>
                <th>数量和频度</th>
                <th>输出媒体</th>
                <th>图形符号说明</th>
                <th>安全保密条件</th>
            </tr>
            <tr>
                <td>节点指针</td>
                <td>TreeNode*</td>
                <td>指针类型</td>
                <td>内存地址</td>
                <td>有效内存地址或nullptr</td>
                <td>返回值</td>
                <td>每次查询1个</td>
                <td>内存</td>
                <td>指向树节点的指针</td>
                <td>无特殊要求</td>
            </tr>
            <tr>
                <td>操作状态</td>
                <td>bool</td>
                <td>布尔类型</td>
                <td>true/false</td>
                <td>{true, false}</td>
                <td>返回值</td>
                <td>每次操作1个</td>
                <td>内存</td>
                <td>操作成功标志</td>
                <td>无特殊要求</td>
            </tr>
        </table>

        <h3>3.6 算法</h3>
        <h4>AVL树插入算法详细步骤：</h4>
        <div class="code-block">TreeNode* Insert(TreeNode* node, const TYPE& value) {
    // 1. 标准BST插入
    if (node == nullptr)
        return new TreeNode(value);

    if (value < node->m_Value)
        node->m_pLeft = Insert(node->m_pLeft, value);
    else if (value > node->m_Value)
        node->m_pRight = Insert(node->m_pRight, value);
    else
        return node; // 重复值不插入

    // 2. 更新高度
    UpdateHeight(node);

    // 3. 获取平衡因子
    int balance = GetBalance(node);

    // 4. 执行旋转操作
    if (balance > 1 && value < node->m_pLeft->m_Value)
        return RightRotate(node);

    if (balance < -1 && value > node->m_pRight->m_Value)
        return LeftRotate(node);

    if (balance > 1 && value > node->m_pLeft->m_Value) {
        node->m_pLeft = LeftRotate(node->m_pLeft);
        return RightRotate(node);
    }

    if (balance < -1 && value < node->m_pRight->m_Value) {
        node->m_pRight = RightRotate(node->m_pRight);
        return LeftRotate(node);
    }

    return node;
}</div>

        <h3>3.7 流程逻辑</h3>
        <div class="flowchart">开始插入操作
    ↓
节点是否为空？
    ↓ 是
创建新节点 → 返回新节点 → 结束
    ↓ 否
值与当前节点比较
    ↓ 小于              ↓ 大于              ↓ 等于
递归插入左子树      递归插入右子树      返回当前节点
    ↓                   ↓                   ↓
更新节点高度 ←??????????????????????????????????????→ 结束
    ↓
计算平衡因子
    ↓
需要旋转？
    ↓ 否
返回当前节点 → 结束
    ↓ 是
判断旋转类型
    ↓ LL        ↓ RR        ↓ LR            ↓ RL
右旋转      左旋转      左旋转+右旋转    右旋转+左旋转
    ↓           ↓           ↓               ↓
返回新根节点 ←??????????????????????????????????????→ 结束</div>

        <h3>3.8 接口</h3>
        <p><strong>上层模块调用关系：</strong></p>
        <div class="code-block">Student Management Module ??→ CAvlTree&lt;CPair&lt;int, Student&gt;&gt;
Course Management Module  ??→ CAvlTree&lt;CPair&lt;int, Course&gt;&gt;
Enrollment Management     ??→ CAvlTree&lt;CPair&lt;int, LinkedList&lt;Enrollment&gt;&gt;&gt;</div>

        <h3>3.9 存储分配</h3>
        <table>
            <tr>
                <th>数据项</th>
                <th>存储类型</th>
                <th>大小(字节)</th>
                <th>生命周期</th>
                <th>分配方式</th>
            </tr>
            <tr>
                <td>TreeNode</td>
                <td>动态分配</td>
                <td>40-80</td>
                <td>插入到删除</td>
                <td>new/delete</td>
            </tr>
            <tr>
                <td>根指针</td>
                <td>静态分配</td>
                <td>8</td>
                <td>程序运行期</td>
                <td>全局变量</td>
            </tr>
        </table>

        <h3>3.10 注释设计</h3>
        <div class="code-block">/**
 * @class CAvlTree
 * @brief AVL平衡二叉搜索树模板类
 * @tparam TYPE 存储的数据类型，必须支持比较操作
 */</div>

        <h3>3.11 限制条件</h3>
        <ul>
            <li>数据类型限制：模板参数TYPE必须支持&lt;、&gt;、==比较操作符重载</li>
            <li>内存限制：受系统可用内存限制</li>
            <li>线程安全限制：当前实现不是线程安全的</li>
        </ul>

        <h3>3.12 测试计划</h3>
        <p><strong>单体测试计划：</strong></p>
        <ul>
            <li>基本功能测试：插入、查找、删除操作</li>
            <li>平衡性测试：各种旋转情况验证</li>
            <li>性能测试：大数据量测试</li>
            <li>边界测试：重复数据、极值处理</li>
        </ul>

        <h3>3.13 尚未解决的问题</h3>
        <ul>
            <li>线程安全性问题需要在后续版本中解决</li>
            <li>内存优化可以考虑实现内存池</li>
            <li>持久化功能需要进一步完善</li>
        </ul>

        <h2>4 程序2（CMyString）设计说明</h2>

        <h3>4.1 程序描述</h3>
        <p>CMyString是自定义字符串类，提供字符串基本操作功能。特点：</p>
        <ul>
            <li>支持深拷贝和移动语义</li>
            <li>兼容C风格字符串</li>
            <li>支持比较操作</li>
            <li>自动内存管理</li>
        </ul>

        <h3>4.2 功能</h3>
        <div class="highlight">
            <p><strong>输入：</strong>C风格字符串或其他CMyString对象</p>
            <p><strong>处理：</strong>字符串存储、操作、比较</p>
            <p><strong>输出：</strong>字符串数据、比较结果</p>
        </div>

        <h3>4.3 性能</h3>
        <ul>
            <li>构造：O(n)</li>
            <li>比较：O(min(m,n))</li>
            <li>连接：O(m+n)</li>
        </ul>

        <h2>5 程序3（CRecordFile）设计说明</h2>

        <h3>5.1 程序描述</h3>
        <p>CRecordFile是文件操作模板类，用于二进制文件读写。特点：</p>
        <ul>
            <li>支持泛型数据类型</li>
            <li>变长记录存储</li>
            <li>支持逻辑删除</li>
            <li>批量数据加载</li>
        </ul>

        <h3>5.2 功能</h3>
        <ul>
            <li>Insert() - 插入记录</li>
            <li>Read() - 读取记录</li>
            <li>Update() - 更新记录</li>
            <li>Delete() - 删除记录</li>
            <li>LoadAllToTree() - 批量加载到AVL树</li>
        </ul>

        <h2>6 系统初始化流程</h2>

        <h3>6.1 初始化步骤</h3>
        <ol>
            <li>CreateStudent() - 从Students1.bin加载学生数据</li>
            <li>CreateCourse() - 从CourseList1.bin加载课程数据</li>
            <li>CreateEnrollment() - 从Selects1.bin加载选课数据</li>
        </ol>

        <h3>6.2 数据索引建立</h3>
        <ul>
            <li>IntAVLStudent：学生ID索引</li>
            <li>StrAVLStudent：学生姓名索引</li>
            <li>IntAVLCourse：课程ID索引</li>
            <li>StrAVLCourse：课程名称索引</li>
            <li>StuAVLEnrollment：学生选课索引</li>
            <li>CouAVLEnrollment：课程选课索引</li>
        </ul>

        <h2>7 主要功能模块</h2>

        <h3>7.1 学生管理模块</h3>
        <ul>
            <li>AddStudent() - 添加学生</li>
            <li>DeleteStudent() - 删除学生</li>
            <li>ModifyStudentName() - 修改学生姓名</li>
            <li>QueryStudentID() - 按ID查询学生</li>
            <li>QueryStudentName() - 按姓名查询学生</li>
        </ul>

        <h3>7.2 课程管理模块</h3>
        <ul>
            <li>AddCourse() - 添加课程</li>
            <li>DeleteCourse() - 删除课程</li>
            <li>ModifyCourseName() - 修改课程名称</li>
            <li>QueryCourseID() - 按ID查询课程</li>
            <li>QueryCourseName() - 按名称查询课程</li>
        </ul>

        <h3>7.3 选课管理模块</h3>
        <ul>
            <li>AddEnrollment() - 添加选课记录</li>
            <li>DeleteEnrollment() - 删除选课记录</li>
            <li>ModifyEnrollmentScore() - 修改选课成绩</li>
            <li>QueryEnrollmentCurID() - 按课程ID查询选课</li>
            <li>QueryEnrollmentStuID() - 按学生ID查询选课</li>
        </ul>

        <h2>8 数据存储设计</h2>

        <h3>8.1 文件结构</h3>
        <ul>
            <li>Students1.bin：学生信息二进制文件</li>
            <li>CourseList1.bin：课程信息二进制文件</li>
            <li>Selects1.bin：选课记录二进制文件</li>
        </ul>

        <h3>8.2 记录格式</h3>
        <div class="code-block">学生记录：[记录长度:4字节][学生ID:4字节][删除标记:1字节][姓名长度:4字节][姓名数据:变长]
课程记录：[记录长度:4字节][课程ID:4字节][删除标记:1字节][课程名长度:4字节][课程名数据:变长]
选课记录：[记录长度:4字节][学生ID:4字节][课程ID:4字节][成绩:4字节][删除标记:1字节]</div>

        <h2>9 性能分析</h2>

        <h3>9.1 时间复杂度</h3>
        <ul>
            <li>查找操作：O(log n)</li>
            <li>插入操作：O(log n)</li>
            <li>删除操作：O(log n)</li>
            <li>遍历操作：O(n)</li>
        </ul>

        <h3>9.2 空间复杂度</h3>
        <ul>
            <li>AVL树存储：O(n)</li>
            <li>索引开销：每个索引O(n)</li>
            <li>总体空间：O(6n)（6个索引）</li>
        </ul>

        <h2>10 系统扩展性</h2>

        <h3>10.1 模板化设计</h3>
        <p>数据结构支持泛型，便于扩展新的数据类型</p>

        <h3>10.2 模块化架构</h3>
        <p>松耦合设计，便于添加新功能模块</p>

        <div class="note">
            <p><strong>文档说明：</strong>本文档为学生选课管理系统的详细设计说明书，包含了系统的核心数据结构、算法实现和接口设计。点击右上角的"下载为Word文档"按钮可以将此文档保存为Word格式。</p>
        </div>
    </div>

    <script>
        function downloadAsWord() {
            const content = document.getElementById('document-content').innerHTML;
            const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>?????ι????????????????</title>
    <style>
        body { font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif; line-height: 1.6; margin: 40px; }
        h1 { color: #2c3e50; font-size: 24px; text-align: center; }
        h2 { color: #34495e; font-size: 20px; border-left: 4px solid #3498db; padding-left: 15px; }
        h3 { color: #2c3e50; font-size: 16px; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #000; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .code-block, .structure-tree, .flowchart { background-color: #f8f9fa; border: 1px solid #ccc; padding: 10px; margin: 10px 0; font-family: "Consolas", "Monaco", monospace; font-size: 12px; }
        .highlight { background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin: 10px 0; }
        .note { background-color: #d1ecf1; padding: 10px; border-left: 4px solid #17a2b8; margin: 10px 0; }
    </style>
</head>
<body>
    ${content}
</body>
</html>`;
            
            const blob = new Blob([htmlContent], { type: 'application/msword' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '?????ι????????????????.doc';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            alert('?????????????');
        }
    </script>
</body>
</html>
