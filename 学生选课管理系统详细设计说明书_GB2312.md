# 学生选课管理系统详细设计说明书

## 1 引言

### 1.1 编写目的

本详细设计说明书的编写目的是为学生选课管理系统的开发提供详细的技术设计方案和实现指导。本文档详细描述了系统的程序结构、各模块的设计考虑、算法实现、接口定义等技术细节，为开发人员提供完整的实现依据。

预期读者包括：
- 系统开发人员
- 系统测试人员
- 系统维护人员
- 项目管理人员
- 技术审查人员

### 1.2 背景

**a. 待开发软件系统名称：** 学生选课管理系统 (StudentSystem)

**b. 项目相关方：**
- 任务提出者：教育管理部门
- 开发者：软件开发团队
- 用户：学校教务管理人员、学生、教师
- 运行环境：Windows操作系统环境下的个人计算机或服务器

### 1.3 定义

- **AVL树**：一种自平衡二叉搜索树，任何节点的两个子树的高度最多相差1
- **CPair**：键值对模板类，用于在AVL树中存储关联数据
- **CMyString**：自定义字符串类，提供字符串操作功能
- **CRecordFile**：记录文件操作类，用于二进制文件的读写操作
- **LinkedList**：自定义链表类，用于处理哈希冲突和多值存储
- **BST**：Binary Search Tree，二叉搜索树
- **RAII**：Resource Acquisition Is Initialization，资源获取即初始化

### 1.4 参考资料

- 《数据结构与算法分析》(C++版) - Mark Allen Weiss
- 《C++ Primer》第5版 - Stanley B. Lippman
- Visual Studio 2022 开发文档
- Windows SDK 10.0 技术文档
- 软件工程国家标准 GB/T 8567-2006

## 2 程序系统的结构

```
StudentSystem (主程序)
├── main() (主函数模块)
├── InitStudentSystem() (系统初始化模块)
├── ShowMenu() (菜单显示模块)
├── Student Management Module (学生管理模块)
│   ├── AddStudent() (添加学生)
│   ├── DeleteStudent() (删除学生)
│   ├── ModifyStudentName() (修改学生姓名)
│   ├── QueryStudentID() (按ID查询学生)
│   └── QueryStudentName() (按姓名查询学生)
├── Course Management Module (课程管理模块)
│   ├── AddCourse() (添加课程)
│   ├── DeleteCourse() (删除课程)
│   ├── ModifyCourseName() (修改课程名称)
│   ├── QueryCourseID() (按ID查询课程)
│   └── QueryCourseName() (按名称查询课程)
├── Enrollment Management Module (选课管理模块)
│   ├── AddEnrollment() (添加选课记录)
│   ├── DeleteEnrollment() (删除选课记录)
│   ├── ModifyEnrollmentScore() (修改选课成绩)
│   ├── QueryEnrollmentCurID() (按课程ID查询选课)
│   └── QueryEnrollmentStuID() (按学生ID查询选课)
├── Data Structure Module (数据结构模块)
│   ├── CAvlTree<T> (AVL树模板类)
│   ├── LinkedList<T> (链表模板类)
│   ├── CustomStack<T> (栈模板类)
│   ├── CustomQueue<T> (队列模板类)
│   └── CPair<K,V> (键值对模板类)
├── Utility Module (工具模块)
│   ├── CMyString (自定义字符串类)
│   ├── CRecordFile<T> (文件操作类)
│   └── AuxiliaryFun (辅助函数)
└── Data Storage Module (数据存储模块)
    ├── Students1.bin (学生数据文件)
    ├── CourseList1.bin (课程数据文件)
    └── Selects1.bin (选课数据文件)
```

## 3 程序1（CAvlTree）设计说明

### 3.1 程序描述

CAvlTree是系统的核心数据结构模块，实现了AVL平衡二叉搜索树。该程序是一个模板类，支持泛型编程，可以存储任意类型的数据。程序特点如下：
- 常驻内存的数据结构
- 可重入的模板类
- 支持并发读操作
- 自动平衡维护
- 高效的查找、插入、删除操作

### 3.2 功能

采用IPO图形式描述：

**输入 (Input)：**
- 泛型数据类型T的对象
- 比较键值
- 操作类型（插入/查找/删除）

**处理 (Process)：**
- 维护AVL树的平衡性
- 执行旋转操作
- 更新节点高度
- 管理树结构

**输出 (Output)：**
- 查询结果节点指针
- 操作成功/失败状态
- 树的统计信息

主要功能包括：
- Insert(value) - 插入节点
- Search(value) - 查找节点
- Delete(value) - 删除节点
- GetRoot() - 获取根节点
- Size() - 获取节点数量
- PreOrder() - 前序遍历
- InOrder() - 中序遍历
- PostOrder() - 后序遍历

### 3.3 性能

- **时间复杂度：** 查找、插入、删除操作均为O(log n)
- **空间复杂度：** O(n)，其中n为节点数量
- **平衡性：** 任意节点的左右子树高度差不超过1
- **并发性：** 支持多线程读操作，写操作需要同步
- **精度要求：** 高度计算精确，平衡因子计算准确
- **灵活性：** 支持任意可比较的数据类型
- **时间特性：** 所有操作保证对数时间复杂度

### 3.4 输入项

| 输入项名称 | 标识符 | 数据类型 | 数据格式 | 有效范围 | 输入方式 | 数量和频度 | 输入媒体 | 数据来源 | 安全保密条件 |
|------------|--------|----------|----------|----------|----------|------------|----------|----------|--------------|
| 节点值 | value | 模板类型T | 依赖于T的定义 | 依赖于T的约束 | 函数参数传递 | 每次操作1个 | 内存 | 调用模块 | 无特殊要求 |
| 比较键 | key | 比较键类型 | 支持<、>、==操作 | 任意可比较类型 | 对象成员访问 | 每次比较1个 | 内存 | 数据对象 | 无特殊要求 |
| 操作标识 | operation | 枚举类型 | INSERT/SEARCH/DELETE | 预定义操作类型 | 函数调用 | 每次操作1个 | 内存 | 调用模块 | 无特殊要求 |

### 3.5 输出项

| 输出项名称 | 标识符 | 数据类型 | 数据格式 | 有效范围 | 输出形式 | 数量和频度 | 输出媒体 | 图形符号说明 | 安全保密条件 |
|------------|--------|----------|----------|----------|----------|------------|----------|--------------|--------------|
| 节点指针 | TreeNode* | 指针类型 | 内存地址 | 有效内存地址或nullptr | 返回值 | 每次查询1个 | 内存 | 指向树节点的指针 | 无特殊要求 |
| 操作状态 | bool | 布尔类型 | true/false | {true, false} | 返回值 | 每次操作1个 | 内存 | 操作成功标志 | 无特殊要求 |
| 节点数量 | int | 整数类型 | 32位整数 | 0到INT_MAX | 返回值 | 按需查询 | 内存 | 树中节点总数 | 无特殊要求 |

### 3.6 算法

**AVL树插入算法详细步骤：**

1. **标准BST插入：**
   ```
   if (node == null)
       return new TreeNode(value)
   if (value < node.value)
       node.left = Insert(node.left, value)
   else if (value > node.value)
       node.right = Insert(node.right, value)
   else
       return node  // 重复值不插入
   ```

2. **更新高度计算公式：**
   ```
   height(node) = 1 + max(height(node.left), height(node.right))
   ```

3. **平衡因子计算公式：**
   ```
   balance_factor = height(node.left) - height(node.right)
   ```

4. **旋转操作计算步骤：**
   - **LL情况：** balance > 1 且 value < node.left.value → 执行右旋转
   - **RR情况：** balance < -1 且 value > node.right.value → 执行左旋转
   - **LR情况：** balance > 1 且 value > node.left.value → 先左旋转后右旋转
   - **RL情况：** balance < -1 且 value < node.right.value → 先右旋转后左旋转

**右旋转算法：**
```cpp
TreeNode* RightRotate(TreeNode* y) {
    TreeNode* x = y->m_pLeft;
    TreeNode* T2 = x->m_pRight;
    
    // 执行旋转
    x->m_pRight = y;
    y->m_pLeft = T2;
    
    // 更新高度
    UpdateHeight(y);
    UpdateHeight(x);
    
    return x;  // 新的根节点
}
```

**左旋转算法：**
```cpp
TreeNode* LeftRotate(TreeNode* x) {
    TreeNode* y = x->m_pRight;
    TreeNode* T2 = y->m_pLeft;
    
    // 执行旋转
    y->m_pLeft = x;
    x->m_pRight = T2;
    
    // 更新高度
    UpdateHeight(x);
    UpdateHeight(y);
    
    return y;  // 新的根节点
}
```
